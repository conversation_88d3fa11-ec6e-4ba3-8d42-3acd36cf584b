* {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "<PERSON><PERSON>", "Oxygen",
    "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue",
    sans-serif;
}

.container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

main {
  padding: 5rem 0;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

main p {
  font-size: 1.2rem;
}

.logo {
  margin-bottom: 2rem;
}

.logo img {
  max-height: 200px;
}

.welcome-title {
  font-weight: 500;
}

.welcome-subtitle {
  font-weight: 400;
  margin-bottom: 3rem;
}

.buttons {
  display: flex;
  flex-direction: row;
}

.buttons .button:not(:last-child) {
  margin-right: 0.5rem;
}

.button {
  border-radius: 3px;
  font-size: 1.2rem;
  padding: 1rem 2rem;
  text-align: center;
  font-weight: 700;
  text-decoration: none;
}

.button-filled {
  border: 2px solid #bf9900;
  background-color: #bf9900;
  color: #f4f4f4;
}

.button-outline {
  border: 2px solid #8a9cff;
  color: #8a9cff;
  background-color: none;
}

code {
  border-radius: 5px;
  padding: 0.2rem;
  background: #efefef;
  font-family: Menlo, Monaco, Lucida Console, Liberation Mono, DejaVu Sans Mono,
    Bitstream Vera Sans Mono, Courier New, monospace;
}
